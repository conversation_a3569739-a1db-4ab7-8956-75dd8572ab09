version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: epkbs-postgres
    environment:
      POSTGRES_DB: epkbs
      POSTGRES_USER: epkbs_user
      POSTGRES_PASSWORD: epkbs_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    networks:
      - epkbs-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U epkbs_user -d epkbs"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: epkbs-redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - epkbs-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Milvus向量数据库
  milvus:
    image: milvusdb/milvus:v2.3.0
    container_name: epkbs-milvus
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
    networks:
      - epkbs-network
    depends_on:
      - etcd
      - minio
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Etcd (Milvus依赖)
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: epkbs-etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    volumes:
      - etcd_data:/etcd
    ports:
      - "2379:2379"
    networks:
      - epkbs-network
    restart: unless-stopped

  # MinIO对象存储 (Milvus依赖)
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: epkbs-minio
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    command: minio server /minio_data --console-address ":9001"
    volumes:
      - minio_data:/minio_data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - epkbs-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # EPKBS主应用
  epkbs-app:
    build: .
    container_name: epkbs-app
    environment:
      # 数据库配置
      DATABASE_URL: ****************************************************/epkbs
      REDIS_URL: redis://:redis_password@redis:6379/0
      
      # Milvus配置
      MILVUS_HOST: milvus
      MILVUS_PORT: 19530
      
      # API配置
      API_HOST: 0.0.0.0
      API_PORT: 8000
      
      # 安全配置
      SECRET_KEY: your-secret-key-change-in-production
      
      # 模型配置
      MODEL_PATH: /app/data/models
      
      # 其他配置
      DEBUG: "false"
      LOG_LEVEL: INFO
      ENABLE_CACHE: "true"
      ENABLE_METRICS: "true"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    ports:
      - "8000:8000"  # API端口
      - "8501:8501"  # Streamlit端口
    networks:
      - epkbs-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      milvus:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: epkbs-nginx
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - epkbs-network
    depends_on:
      - epkbs-app
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  milvus_data:
    driver: local
  etcd_data:
    driver: local
  minio_data:
    driver: local

networks:
  epkbs-network:
    driver: bridge
