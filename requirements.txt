# 核心框架
langchain>=0.1.0
langchain-community>=0.0.20
langchain-experimental>=0.0.50
langchain-openai>=0.0.8

# LLM和嵌入模型
transformers>=4.36.0
torch>=2.1.0
sentence-transformers>=2.2.2
accelerate>=0.25.0

# 向量数据库
pymilvus>=2.3.0
milvus>=2.3.0

# 关系数据库
psycopg2-binary>=2.9.7
sqlalchemy>=2.0.0
alembic>=1.12.0

# 缓存
redis>=5.0.0
hiredis>=2.2.3

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
streamlit>=1.28.0
pydantic>=2.5.0

# 文档处理
unstructured[all-docs]>=0.11.0
pypdf>=3.17.0
python-docx>=1.1.0
python-pptx>=0.6.23
openpyxl>=3.1.2

# 数据处理
pandas>=2.1.0
numpy>=1.24.0
scikit-learn>=1.3.0

# 检索和重排序
rank-bm25>=0.2.2
faiss-cpu>=1.7.4
colbert-ai>=0.2.19

# 图神经网络
torch-geometric>=2.4.0
networkx>=3.2.1
neo4j>=5.14.0

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0
aiohttp>=3.9.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# 日志和监控
loguru>=0.7.2
prometheus-client>=0.19.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 代码质量
black>=23.10.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0

# 开发工具
jupyter>=1.0.0
ipython>=8.17.0
tqdm>=4.66.0

# 安全
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# 其他工具
python-multipart>=0.0.6
jinja2>=3.1.2
click>=8.1.7
rich>=13.7.0
