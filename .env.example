# 企业私有知识库系统环境变量配置

# 基础配置
DEBUG=false
SECRET_KEY=your-super-secret-key-change-this-in-production

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# PostgreSQL数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=epkbs
POSTGRES_USER=epkbs_user
POSTGRES_PASSWORD=epkbs_pass

# Redis缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Milvus向量数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=

# Qwen3模型配置 - 最新系列
QWEN_MODEL_PATH=Qwen/Qwen3-8B
EMBEDDING_MODEL_PATH=Qwen/Qwen3-Embedding-8B
RERANKER_MODEL_PATH=Qwen/Qwen3-Reranker-8B

# 模型推理参数
MAX_TOKENS=2048
TEMPERATURE=0.7
TOP_P=0.9

# RAG配置
CHUNK_SIZE=512
CHUNK_OVERLAP=50
TOP_K=10
SIMILARITY_THRESHOLD=0.7

# 检索权重配置
VECTOR_SEARCH_WEIGHT=0.6
SPARSE_SEARCH_WEIGHT=0.4
RERANK_TOP_K=5

# 缓存配置
CACHE_TTL=3600
ENABLE_CACHE=true

# 安全配置
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
MAX_FILE_SIZE=104857600  # 100MB

# 日志配置
LOG_LEVEL=INFO

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=8001
